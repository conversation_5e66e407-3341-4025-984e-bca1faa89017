<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Audio Player</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: transparent;
    }
  </style>
</head>
<body>
  <audio id="background-audio" loop preload="auto">
    <source src="song/Can't Help Falling in Love - Haley Reinhart.mp3" type="audio/mpeg">
  </audio>

  <script>
    class PersistentAudioPlayer {
      constructor() {
        this.audio = document.getElementById('background-audio');
        this.isPlaying = false;
        this.volume = 0.3;
        this.storageKey = 'proposalWebsiteAudio';
        
        this.loadState();
        this.setupAudio();
        this.setupMessageListener();
      }

      setupAudio() {
        this.audio.volume = this.volume;
        
        this.audio.addEventListener('timeupdate', () => {
          this.saveState();
          // Notify parent of time update
          this.postMessage('timeupdate', {
            currentTime: this.audio.currentTime,
            duration: this.audio.duration
          });
        });

        this.audio.addEventListener('loadeddata', () => {
          // Restore playback position if we have one
          const saved = this.getSavedState();
          if (saved && saved.currentTime > 0) {
            this.audio.currentTime = saved.currentTime;
          }
          
          // Auto-play if it was playing before
          if (saved && saved.isPlaying) {
            this.play();
          }
        });

        this.audio.addEventListener('play', () => {
          this.isPlaying = true;
          this.saveState();
          this.postMessage('play');
        });

        this.audio.addEventListener('pause', () => {
          this.isPlaying = false;
          this.saveState();
          this.postMessage('pause');
        });

        this.audio.addEventListener('error', (e) => {
          console.warn('Audio error:', e);
          this.postMessage('error', { error: e.message });
        });
      }

      setupMessageListener() {
        window.addEventListener('message', (event) => {
          const { action, data } = event.data;
          
          switch (action) {
            case 'play':
              this.play();
              break;
            case 'pause':
              this.pause();
              break;
            case 'toggle':
              this.toggle();
              break;
            case 'setVolume':
              this.setVolume(data.volume);
              break;
            case 'getState':
              this.postMessage('state', this.getState());
              break;
          }
        });
      }

      play() {
        const playPromise = this.audio.play();
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              this.isPlaying = true;
              this.saveState();
              this.postMessage('playSuccess');
            })
            .catch((error) => {
              console.warn('Audio play failed:', error);
              this.postMessage('playFailed', { error: error.message });
            });
        }
      }

      pause() {
        this.audio.pause();
        this.isPlaying = false;
        this.saveState();
      }

      toggle() {
        if (this.isPlaying) {
          this.pause();
        } else {
          this.play();
        }
      }

      setVolume(vol) {
        this.volume = Math.max(0, Math.min(1, vol));
        this.audio.volume = this.volume;
        this.saveState();
        this.postMessage('volumeChanged', { volume: this.volume });
      }

      getState() {
        return {
          isPlaying: this.isPlaying,
          currentTime: this.audio.currentTime,
          duration: this.audio.duration,
          volume: this.volume
        };
      }

      saveState() {
        const state = {
          isPlaying: this.isPlaying,
          currentTime: this.audio.currentTime,
          volume: this.volume,
          timestamp: Date.now()
        };
        try {
          localStorage.setItem(this.storageKey, JSON.stringify(state));
        } catch (e) {
          // Handle localStorage errors silently
        }
      }

      getSavedState() {
        try {
          const saved = localStorage.getItem(this.storageKey);
          return saved ? JSON.parse(saved) : null;
        } catch (e) {
          return null;
        }
      }

      loadState() {
        const saved = this.getSavedState();
        if (saved) {
          this.isPlaying = saved.isPlaying || false;
          this.volume = saved.volume || 0.3;
        }
      }

      postMessage(type, data = {}) {
        if (window.parent !== window) {
          window.parent.postMessage({
            source: 'audio-player',
            type,
            data
          }, '*');
        }
      }
    }

    // Initialize the audio player
    new PersistentAudioPlayer();
  </script>
</body>
</html>
