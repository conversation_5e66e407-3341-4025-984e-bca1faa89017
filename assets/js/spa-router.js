// Single Page Application Router for seamless navigation
class SPARouter {
  constructor() {
    this.routes = {
      '/': 'index.html',
      '/timeline': 'timeline.html',
      '/proposal': 'proposal.html'
    };
    this.currentPage = '/';
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle browser back/forward buttons
    window.addEventListener('popstate', (e) => {
      if (e.state && e.state.page) {
        this.loadPage(e.state.page, false);
      }
    });

    // Intercept navigation clicks
    document.addEventListener('click', (e) => {
      const link = e.target.closest('[data-spa-link]');
      if (link) {
        e.preventDefault();
        const page = link.getAttribute('data-spa-link');
        this.navigateTo(page);
      }
    });
  }

  async navigateTo(page) {
    if (page === this.currentPage) return;
    
    // Add to browser history
    history.pushState({ page }, '', page === '/' ? '/' : page);
    
    await this.loadPage(page, true);
  }

  async loadPage(page, addTransition = true) {
    try {
      // Show transition if requested
      if (addTransition) {
        this.showTransition();
      }

      // Ensure we have a stable container that we can swap without touching persistent elements
      const container = this.getContainer();

      // Fetch the page content
      const response = await fetch(this.routes[page] || 'index.html');
      if (!response.ok) throw new Error(`Failed to load ${page}`);

      const html = await response.text();

      // Parse the HTML to extract main/body content
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      const newMain = doc.querySelector('main') || doc.body;
      const newTitle = doc.title || document.title;
      const newBodyClass = doc.body.getAttribute('class') || '';

      // Update page title and body class to match the target page styles
      document.title = newTitle;
      document.body.className = newBodyClass;

      // Replace ONLY the container's content to avoid detaching the audio iframe/controls
      // Clone incoming content and strip out any script/noscript tags to prevent double-loading
      const contentRoot = newMain.cloneNode(true);
      contentRoot.querySelectorAll('script, noscript').forEach(n => n.remove());

      container.innerHTML = '';
      while (contentRoot.firstChild) {
        container.appendChild(contentRoot.firstChild);
      }

      // Load page-specific scripts under our control
      await this.loadPageScripts(page);

      // Update current page
      this.currentPage = page;

      // Notify listeners that a SPA page finished loading
      window.dispatchEvent(new CustomEvent('spa-page-loaded', { detail: { page } }));

      // Hide transition
      if (addTransition) {
        setTimeout(() => this.hideTransition(), 100);
      }

    } catch (error) {
      console.error('Failed to load page:', error);
      // Fallback to regular navigation
      window.location.href = this.routes[page] || '/';
    }
  }

  // Get or create stable SPA container that excludes persistent audio elements
  getContainer() {
    let container = document.getElementById('spa-root');
    if (container) return container;

    container = document.createElement('div');
    container.id = 'spa-root';

    // Collect persistent elements we should NOT move into the container
    const persistent = new Set();
    const selectors = ['#persistent-audio-player', '.audio-controls', '.audio-play-prompt'];
    selectors.forEach(sel => {
      document.querySelectorAll(sel).forEach(el => persistent.add(el));
    });

    // Move all current non-persistent children into the container
    Array.from(document.body.children).forEach(child => {
      if (!persistent.has(child) && !child.classList.contains('spa-transition')) {
        container.appendChild(child);
      }
    });

    document.body.appendChild(container);
    return container;
  }

  async loadPageScripts(page) {
    // Remove existing page scripts
    const existingScripts = document.querySelectorAll('script[data-page-script]');
    existingScripts.forEach(script => script.remove());

    // Load appropriate scripts based on page
    const scriptMap = {
      '/': ['assets/js/landing.js'],
      '/timeline': ['assets/js/timeline.js'],
      '/proposal': ['assets/js/proposal.js']
    };

    const scripts = scriptMap[page] || [];
    
    for (const scriptSrc of scripts) {
      await this.loadScript(scriptSrc);
    }
  }

  loadScript(src) {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.type = 'module';
      script.src = src;
      script.setAttribute('data-page-script', 'true');
      
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      
      document.head.appendChild(script);
    });
  }

  showTransition() {
    let transition = document.querySelector('.spa-transition');
    if (!transition) {
      transition = document.createElement('div');
      transition.className = 'spa-transition';
      transition.style.cssText = `
        position: fixed;
        inset: 0;
        background: var(--rose-50, #fff5f7);
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      `;
      document.body.appendChild(transition);
    }
    
    // Trigger transition
    requestAnimationFrame(() => {
      transition.style.opacity = '1';
    });
  }

  hideTransition() {
    const transition = document.querySelector('.spa-transition');
    if (transition) {
      transition.style.opacity = '0';
      setTimeout(() => {
        if (transition.parentNode) {
          transition.remove();
        }
      }, 300);
    }
  }

  // Initialize the router
  init() {
    // Determine current page from URL
    const path = window.location.pathname;
    this.currentPage = path === '/' ? '/' : 
                     path.includes('timeline') ? '/timeline' :
                     path.includes('proposal') ? '/proposal' : '/';
    
    // Set initial history state
    history.replaceState({ page: this.currentPage }, '', this.currentPage);
  }
}

// Create global router instance
window.spaRouter = window.spaRouter || new SPARouter();

export default window.spaRouter;
