import { mountFloatingHearts } from './utils.js';
import audioManager from './audio-manager.js';

window.addEventListener('DOMContentLoaded', () => {
  const cleanup = mountFloatingHearts(document.body, 20);

  // Initialize audio manager and create controls
  audioManager.createControls();
  audioManager.init();

  const btn = document.getElementById('open-envelope');
  btn?.addEventListener('click', () => {
    // Small click animation
    btn.style.transition = 'transform .35s ease';
    btn.style.transform = 'translateY(-6px) scale(1.04)';
    setTimeout(() => {
      if (window.spaRouter && typeof window.spaRouter.navigateTo === 'function') {
        window.spaRouter.navigateTo('/timeline');
      } else {
        window.location.href = 'timeline.html';
      }
    }, 380);
  });
});

