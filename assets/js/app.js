// Main SPA application entry point
import audioManager from './audio-manager.js';
import spaRouter from './spa-router.js';
import { mountFloatingHearts } from './utils.js';

class ProposalApp {
  constructor() {
    this.currentPageCleanup = null;
    this.init();
  }

  async init() {
    // Initialize audio manager first
    audioManager.createControls();
    audioManager.init();

    // Initialize SPA router
    spaRouter.init();

    // Load initial page content
    await this.loadInitialPage();

    // Setup global event listeners
    this.setupGlobalListeners();
  }

  async loadInitialPage() {
    // Determine which page we're on based on URL
    const path = window.location.pathname;
    let page = '/';
    
    if (path.includes('timeline')) {
      page = '/timeline';
    } else if (path.includes('proposal')) {
      page = '/proposal';
    }

    // Load the appropriate page content
    if (page !== '/') {
      await spaRouter.loadPage(page, false);
    } else {
      // We're already on the landing page, just initialize it
      this.initLandingPage();
    }
  }

  setupGlobalListeners() {
    // Listen for page changes to initialize page-specific functionality
    window.addEventListener('spa-page-loaded', (e) => {
      this.initPageFunctionality(e.detail.page);
    });
  }

  initPageFunctionality(page) {
    // Clean up previous page
    if (this.currentPageCleanup) {
      this.currentPageCleanup();
      this.currentPageCleanup = null;
    }

    // Initialize page-specific functionality
    switch (page) {
      case '/':
        this.currentPageCleanup = this.initLandingPage();
        break;
      case '/timeline':
        this.currentPageCleanup = this.initTimelinePage();
        break;
      case '/proposal':
        this.currentPageCleanup = this.initProposalPage();
        break;
    }
  }

  initLandingPage() {
    const cleanup = mountFloatingHearts(document.body, 20);
    
    const btn = document.getElementById('open-envelope');
    const clickHandler = () => {
      // Small click animation
      btn.style.transition = 'transform .35s ease';
      btn.style.transform = 'translateY(-6px) scale(1.04)';
      setTimeout(() => {
        spaRouter.navigateTo('/timeline');
      }, 380);
    };

    if (btn) {
      btn.addEventListener('click', clickHandler);
    }

    // Return cleanup function
    return () => {
      cleanup();
      if (btn) {
        btn.removeEventListener('click', clickHandler);
      }
    };
  }

  initTimelinePage() {
    const cleanup = mountFloatingHearts(document.body, 14);
    
    // Timeline-specific initialization will be handled by the dynamically loaded script
    // This is just for any global timeline setup
    
    return cleanup;
  }

  initProposalPage() {
    const cleanup = mountFloatingHearts(document.body, 26);
    
    // Proposal-specific initialization will be handled by the dynamically loaded script
    // This is just for any global proposal setup
    
    return cleanup;
  }
}

// Initialize the app when DOM is ready
window.addEventListener('DOMContentLoaded', () => {
  new ProposalApp();
});

// Export for potential external use
export default ProposalApp;
